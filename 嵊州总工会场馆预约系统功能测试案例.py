#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
嵊州总工会场馆预约系统功能测试案例生成器
生成Excel格式的测试案例文档
"""

import pandas as pd
from datetime import datetime

def create_test_cases():
    """创建测试案例数据"""
    test_cases = []
    
    # H5端测试案例
    
    # 1. 首页模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_001",
            "用例标题": "首页UI界面显示验证",
            "功能模块": "H5端-首页",
            "前提条件": "用户已打开H5页面",
            "测试步骤": "1. 打开H5首页\n2. 检查页面布局和UI元素\n3. 验证页面响应式设计",
            "预期结果": "首页UI界面正常显示，布局合理，响应式设计正常",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_002",
            "用例标题": "首页功能导航验证",
            "功能模块": "H5端-首页",
            "前提条件": "用户已打开H5首页",
            "测试步骤": "1. 点击场馆预约入口\n2. 点击个人中心入口\n3. 验证各导航功能",
            "预期结果": "各导航功能正常跳转到对应页面",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        }
    ])
    
    # 2. 场馆预约模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_003",
            "用例标题": "乒乓球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择乒乓球场地\n3. 选择1-5号桌中的任意一桌\n4. 选择可预约时间段\n5. 填写预约人员信息（最多2人）\n6. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_004",
            "用例标题": "篮球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择篮球场地\n3. 选择可预约时间段\n4. 填写预约人员信息（最多12人）\n5. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_005",
            "用例标题": "气排球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择气排球场地\n3. 选择可预约时间段\n4. 填写预约人员信息\n5. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_006",
            "用例标题": "乒乓球场地预约-超出人数限制",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择乒乓球场地\n3. 选择任意一桌\n4. 尝试添加超过2人的预约信息\n5. 提交预约",
            "预期结果": "系统提示人数超出限制，预约失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_007",
            "用例标题": "篮球场地预约-超出人数限制",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择篮球场地\n3. 尝试添加超过12人的预约信息\n4. 提交预约",
            "预期结果": "系统提示人数超出限制，预约失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_008",
            "用例标题": "场地开放时间验证-周二至周四晚上",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为周二至周四",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段\n3. 验证时间段为18:30-20:30",
            "预期结果": "只显示18:30-20:30时间段可预约",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_009",
            "用例标题": "场地开放时间验证-周六周日全天",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为周六或周日",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段\n3. 验证时间段包含：9:00-11:00、14:30-16:30、18:30-20:30",
            "预期结果": "显示三个时间段均可预约",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_010",
            "用例标题": "节假日闭馆验证",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为节假日",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段",
            "预期结果": "显示闭馆提示，无可预约时间段",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        }
    ])
    
    # 3. 预约规则模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_011",
            "用例标题": "未关注公众号用户预约验证",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户未关注公众号",
            "测试步骤": "1. 尝试进入场馆预约页面\n2. 或尝试提交预约",
            "预期结果": "系统提示需要先关注公众号，无法进行预约",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_012",
            "用例标题": "个人信息绑定-首次绑定",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已关注公众号但未绑定个人信息",
            "测试步骤": "1. 进入个人信息绑定页面\n2. 填写姓名、手机号、身份证号、单位名称\n3. 提交绑定信息",
            "预期结果": "绑定成功，信息存入数据库",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_013",
            "用例标题": "个人信息绑定-信息校验",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已关注公众号",
            "测试步骤": "1. 进入个人信息绑定页面\n2. 填写不完整或格式错误的信息\n3. 提交绑定信息",
            "预期结果": "系统提示信息格式错误或不完整，绑定失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_014",
            "用例标题": "预约信息自动填充验证",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已绑定个人信息并有历史预约记录",
            "测试步骤": "1. 进入场馆预约页面\n2. 在预约人员信息中输入已存在的姓名\n3. 观察其他信息是否自动填充",
            "预期结果": "根据姓名自动带出手机号、身份证号、单位名称",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_015",
            "用例标题": "个人中心查看预约记录",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已有预约记录",
            "测试步骤": "1. 进入个人中心\n2. 查看我的预约\n3. 验证预约记录显示",
            "预期结果": "正确显示所有预约记录，包括预约状态、时间、场地等信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_016",
            "用例标题": "个人中心取消预约",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户有可取消的预约记录",
            "测试步骤": "1. 进入个人中心\n2. 选择要取消的预约\n3. 点击取消预约\n4. 确认取消操作",
            "预期结果": "预约成功取消，状态更新为已取消",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P1"
        }
    ])
    
    return test_cases

def create_pc_test_cases():
    """创建PC端测试案例"""
    pc_test_cases = []

    # PC端测试案例

    # 1. 场地管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_001",
            "用例标题": "场地管理页面显示验证",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 点击左侧菜单'体育场馆'\n2. 进入场地管理页面\n3. 验证页面显示场地列表\n4. 检查场地名称、场地简介、场地位置、场地类型、创建时间、发布状态、操作等字段",
            "预期结果": "场地管理页面正常显示，包含所有必要字段和操作按钮",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_002",
            "用例标题": "场地名称搜索功能",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在多个场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 在场地名称搜索框输入场地名称关键字\n3. 点击查询按钮\n4. 验证搜索结果",
            "预期结果": "正确显示包含关键字的场地信息，搜索功能正常",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_003",
            "用例标题": "新增场地信息",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 进入场地管理页面\n2. 点击'新增'按钮\n3. 填写场地名称、场地简介、场地位置等基本信息\n4. 设置场地类型和容量限制\n5. 点击保存",
            "预期结果": "场地信息新增成功，在场地列表中显示新增的场地",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_004",
            "用例标题": "编辑场地信息",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 选择要修改的场地\n3. 点击操作列的'编辑'按钮\n4. 修改场地信息\n5. 点击保存",
            "预期结果": "场地信息修改成功，列表中显示更新后的信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_005",
            "用例标题": "删除场地信息",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在可删除的场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 选择要删除的场地\n3. 点击操作列的'删除'按钮\n4. 确认删除操作",
            "预期结果": "场地信息删除成功，从列表中移除",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        },
        {
            "用例编号": "TC_PC_006",
            "用例标题": "场地发布状态管理",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 查看场地的发布状态\n3. 点击操作列的相关按钮修改发布状态\n4. 验证状态变更",
            "预期结果": "场地发布状态正确变更，H5端相应显示或隐藏该场地",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        }
    ])
    
    # 2. 订单管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_007",
            "用例标题": "订单管理页面显示验证",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 点击左侧菜单'订单管理'\n2. 进入订单管理页面\n3. 验证页面显示订单列表\n4. 检查订单状态、订单日期、订单时间、场地名称、场地类型、预定人姓名、预定人数量、预定人单位、创建时间、操作等字段",
            "预期结果": "订单管理页面正常显示，包含所有必要字段和筛选条件",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_008",
            "用例标题": "订单状态筛选功能",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在不同状态的订单",
            "测试步骤": "1. 进入订单管理页面\n2. 点击订单状态下拉框\n3. 选择特定状态（如'正常'、'取消'等）\n4. 点击查询按钮\n5. 验证筛选结果",
            "预期结果": "筛选功能正常，只显示对应状态的订单",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_009",
            "用例标题": "订单时间筛选功能",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在不同时间的订单",
            "测试步骤": "1. 进入订单管理页面\n2. 设置订单时间筛选条件\n3. 选择开始时间和结束时间\n4. 点击查询按钮\n5. 验证筛选结果",
            "预期结果": "正确显示指定时间范围内的订单",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_010",
            "用例标题": "预定人筛选功能",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在多个预定人的订单",
            "测试步骤": "1. 进入订单管理页面\n2. 在预定人输入框输入预定人姓名\n3. 点击查询按钮\n4. 验证筛选结果",
            "预期结果": "正确显示该预定人的所有订单",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_011",
            "用例标题": "查看订单详细信息",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在预约订单",
            "测试步骤": "1. 进入订单管理页面\n2. 选择某个订单\n3. 点击操作列的'详情'或'查看'按钮\n4. 验证订单详细信息显示",
            "预期结果": "正确显示订单的详细信息，包括所有报名人员的完整信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_012",
            "用例标题": "订单数据导出功能",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在订单数据",
            "测试步骤": "1. 进入订单管理页面\n2. 设置筛选条件（可选）\n3. 点击导出按钮\n4. 验证导出文件",
            "预期结果": "成功导出订单数据，文件格式正确，数据完整",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        },
        {
            "用例编号": "TC_PC_013",
            "用例标题": "订单分页功能验证",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在大量订单数据",
            "测试步骤": "1. 进入订单管理页面\n2. 查看页面底部分页控件\n3. 点击不同页码\n4. 验证分页功能",
            "预期结果": "分页功能正常，能够正确显示不同页的订单数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        }
    ])
    
    # 3. 用户管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_014",
            "用例标题": "用户管理页面显示验证",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 点击左侧菜单'用户管理'\n2. 进入用户管理页面\n3. 验证页面显示用户列表\n4. 检查用户名、手机号、违约次数、当前状态、创建时间、操作等字段",
            "预期结果": "用户管理页面正常显示，包含所有必要字段和搜索功能",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_015",
            "用例标题": "用户名搜索功能",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在多个绑定用户",
            "测试步骤": "1. 进入用户管理页面\n2. 在用户名搜索框输入用户名关键字\n3. 点击查询按钮\n4. 验证搜索结果",
            "预期结果": "正确显示包含关键字的用户信息，搜索功能正常",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_016",
            "用例标题": "手机号搜索功能",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在多个绑定用户",
            "测试步骤": "1. 进入用户管理页面\n2. 在手机号搜索框输入手机号\n3. 点击查询按钮\n4. 验证搜索结果",
            "预期结果": "正确显示对应手机号的用户信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_017",
            "用例标题": "查看用户详细信息",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在绑定用户",
            "测试步骤": "1. 进入用户管理页面\n2. 选择某个用户\n3. 点击操作列的'详情'按钮\n4. 验证用户详细信息显示",
            "预期结果": "正确显示用户的完整个人信息，包括姓名、手机号、身份证号、单位名称等",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_018",
            "用例标题": "用户违约次数统计验证",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在有违约记录的用户",
            "测试步骤": "1. 进入用户管理页面\n2. 查看用户列表中的违约次数字段\n3. 选择有违约记录的用户查看详情\n4. 验证违约次数统计准确性",
            "预期结果": "违约次数统计准确，与实际违约情况一致",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_019",
            "用例标题": "用户状态管理",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在绑定用户",
            "测试步骤": "1. 进入用户管理页面\n2. 查看用户的当前状态\n3. 点击操作列的状态管理按钮\n4. 修改用户状态\n5. 验证状态变更",
            "预期结果": "用户状态正确变更，影响用户在H5端的使用权限",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        },
        {
            "用例编号": "TC_PC_020",
            "用例标题": "用户数据导出功能",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在用户数据",
            "测试步骤": "1. 进入用户管理页面\n2. 设置筛选条件（可选）\n3. 点击导出按钮\n4. 验证导出文件",
            "预期结果": "成功导出用户数据，文件格式正确，数据完整",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        }
    ])
    
    # 4. 订单统计模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_021",
            "用例标题": "订单统计页面显示验证",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 点击左侧菜单'订单统计'\n2. 进入订单统计页面\n3. 验证页面显示统计筛选条件\n4. 检查场地名称、场地类型、订单状态、创建时间等筛选字段",
            "预期结果": "订单统计页面正常显示，包含所有筛选条件和查询按钮",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_022",
            "用例标题": "按场地名称统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在不同场地的订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 在场地名称下拉框选择特定场地\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确统计指定场地的订单数量和相关数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_023",
            "用例标题": "按场地类型统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在不同类型场地的订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 在场地类型下拉框选择特定类型\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确统计指定类型场地的订单数量和相关数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_024",
            "用例标题": "按订单状态统计",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在不同状态的订单",
            "测试步骤": "1. 进入订单统计页面\n2. 在订单状态下拉框选择特定状态\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确统计不同状态订单的数量和占比",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_025",
            "用例标题": "按创建时间段统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在历史订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 设置创建时间的开始和结束时间\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确统计指定时间段内的订单数量和相关数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_026",
            "用例标题": "综合条件统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在历史订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 同时设置场地名称、场地类型、订单状态、创建时间等多个筛选条件\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确统计满足多个条件的订单数据，统计结果准确",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_027",
            "用例标题": "统计结果显示验证",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，已执行统计查询",
            "测试步骤": "1. 进入订单统计页面\n2. 设置筛选条件并执行查询\n3. 验证统计结果的显示格式\n4. 检查统计数据的准确性",
            "预期结果": "统计结果正确显示，包含场地名称、场地类型、开始时间、结束时间、订单状态、总参与人数等信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_028",
            "用例标题": "统计数据导出功能",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，已执行统计查询",
            "测试步骤": "1. 进入订单统计页面\n2. 设置筛选条件并执行查询\n3. 点击导出按钮\n4. 验证导出文件",
            "预期结果": "成功导出统计数据，文件格式正确，数据完整",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        },
        {
            "用例编号": "TC_PC_029",
            "用例标题": "无数据时统计结果验证",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 进入订单统计页面\n2. 设置不存在数据的筛选条件\n3. 点击查询按钮\n4. 查看统计结果",
            "预期结果": "正确显示'暂无数据'提示，不出现错误信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        }
    ])
    
    # 5. 系统通用功能测试案例
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_030",
            "用例标题": "PC端登录功能验证",
            "功能模块": "PC端-系统功能",
            "前提条件": "系统已部署，存在管理员账号",
            "测试步骤": "1. 打开PC端登录页面\n2. 输入正确的用户名和密码\n3. 点击登录按钮\n4. 验证登录成功",
            "预期结果": "登录成功，跳转到系统首页，显示用户信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_031",
            "用例标题": "PC端登录失败验证",
            "功能模块": "PC端-系统功能",
            "前提条件": "系统已部署",
            "测试步骤": "1. 打开PC端登录页面\n2. 输入错误的用户名或密码\n3. 点击登录按钮\n4. 验证登录失败提示",
            "预期结果": "登录失败，显示错误提示信息",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_032",
            "用例标题": "左侧菜单导航功能",
            "功能模块": "PC端-系统功能",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 查看左侧菜单栏\n2. 依次点击各个菜单项\n3. 验证页面跳转功能\n4. 检查菜单高亮状态",
            "预期结果": "菜单导航功能正常，页面正确跳转，当前菜单高亮显示",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_033",
            "用例标题": "用户信息显示和退出功能",
            "功能模块": "PC端-系统功能",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 查看页面右上角用户信息显示\n2. 点击用户头像或用户名\n3. 选择退出登录\n4. 验证退出功能",
            "预期结果": "用户信息正确显示，退出功能正常，返回登录页面",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_034",
            "用例标题": "页面响应式布局验证",
            "功能模块": "PC端-系统功能",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 调整浏览器窗口大小\n2. 验证页面布局适应性\n3. 检查各功能模块显示\n4. 验证操作功能正常",
            "预期结果": "页面布局能够适应不同窗口大小，功能正常使用",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P2"
        }
    ])

    return pc_test_cases

def generate_excel_file():
    """生成Excel测试案例文档"""
    # 获取H5端和PC端测试案例
    h5_cases = create_test_cases()
    pc_cases = create_pc_test_cases()
    
    # 合并所有测试案例
    all_cases = h5_cases + pc_cases
    
    # 创建DataFrame
    df = pd.DataFrame(all_cases)
    
    # 重新排列列的顺序
    column_order = [
        "用例编号", "用例标题", "功能模块", "前提条件", 
        "测试步骤", "预期结果", "用例属性", "用例类型", "用例等级"
    ]
    df = df[column_order]
    
    # 生成Excel文件
    filename = f"嵊州总工会场馆预约系统功能测试案例_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入所有测试案例到主表
        df.to_excel(writer, sheet_name='功能测试案例', index=False)
        
        # 按模块分别创建工作表
        h5_df = pd.DataFrame(h5_cases)[column_order]
        pc_df = pd.DataFrame(pc_cases)[column_order]
        
        h5_df.to_excel(writer, sheet_name='H5端测试案例', index=False)
        pc_df.to_excel(writer, sheet_name='PC端测试案例', index=False)
        
        # 调整列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    return filename

if __name__ == "__main__":
    try:
        filename = generate_excel_file()
        print(f"测试案例Excel文档已生成: {filename}")
        print(f"共生成 {len(create_test_cases()) + len(create_pc_test_cases())} 个测试案例")
        print("\n测试案例包含以下模块:")
        print("H5端:")
        print("- 首页模块")
        print("- 场馆预约模块") 
        print("- 预约规则模块")
        print("\nPC端:")
        print("- 场地管理模块")
        print("- 订单管理模块")
        print("- 用户管理模块")
        print("- 订单统计模块")
        print("- 系统功能模块")
    except Exception as e:
        print(f"生成Excel文档时出错: {str(e)}")
