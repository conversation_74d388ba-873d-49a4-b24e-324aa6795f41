#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
嵊州总工会场馆预约系统功能测试案例生成器
生成Excel格式的测试案例文档
"""

import pandas as pd
from datetime import datetime

def create_test_cases():
    """创建测试案例数据"""
    test_cases = []
    
    # H5端测试案例
    
    # 1. 首页模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_001",
            "用例标题": "首页UI界面显示验证",
            "功能模块": "H5端-首页",
            "前提条件": "用户已打开H5页面",
            "测试步骤": "1. 打开H5首页\n2. 检查页面布局和UI元素\n3. 验证页面响应式设计",
            "预期结果": "首页UI界面正常显示，布局合理，响应式设计正常",
            "用例属性": "正用例",
            "用例类型": "界面",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_002",
            "用例标题": "首页功能导航验证",
            "功能模块": "H5端-首页",
            "前提条件": "用户已打开H5首页",
            "测试步骤": "1. 点击场馆预约入口\n2. 点击个人中心入口\n3. 验证各导航功能",
            "预期结果": "各导航功能正常跳转到对应页面",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        }
    ])
    
    # 2. 场馆预约模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_003",
            "用例标题": "乒乓球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择乒乓球场地\n3. 选择1-5号桌中的任意一桌\n4. 选择可预约时间段\n5. 填写预约人员信息（最多2人）\n6. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_004",
            "用例标题": "篮球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择篮球场地\n3. 选择可预约时间段\n4. 填写预约人员信息（最多12人）\n5. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_005",
            "用例标题": "气排球场地预约-正常预约",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择气排球场地\n3. 选择可预约时间段\n4. 填写预约人员信息\n5. 提交预约",
            "预期结果": "预约成功，系统提示预约成功信息，订单生成",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_006",
            "用例标题": "乒乓球场地预约-超出人数限制",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择乒乓球场地\n3. 选择任意一桌\n4. 尝试添加超过2人的预约信息\n5. 提交预约",
            "预期结果": "系统提示人数超出限制，预约失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_007",
            "用例标题": "篮球场地预约-超出人数限制",
            "功能模块": "H5端-场馆预约",
            "前提条件": "用户已关注公众号并绑定个人信息",
            "测试步骤": "1. 进入场馆预约页面\n2. 选择篮球场地\n3. 尝试添加超过12人的预约信息\n4. 提交预约",
            "预期结果": "系统提示人数超出限制，预约失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_008",
            "用例标题": "场地开放时间验证-周二至周四晚上",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为周二至周四",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段\n3. 验证时间段为18:30-20:30",
            "预期结果": "只显示18:30-20:30时间段可预约",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_009",
            "用例标题": "场地开放时间验证-周六周日全天",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为周六或周日",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段\n3. 验证时间段包含：9:00-11:00、14:30-16:30、18:30-20:30",
            "预期结果": "显示三个时间段均可预约",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_010",
            "用例标题": "节假日闭馆验证",
            "功能模块": "H5端-场馆预约",
            "前提条件": "当前时间为节假日",
            "测试步骤": "1. 进入场馆预约页面\n2. 查看可预约时间段",
            "预期结果": "显示闭馆提示，无可预约时间段",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        }
    ])
    
    # 3. 预约规则模块测试案例
    test_cases.extend([
        {
            "用例编号": "TC_H5_011",
            "用例标题": "未关注公众号用户预约验证",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户未关注公众号",
            "测试步骤": "1. 尝试进入场馆预约页面\n2. 或尝试提交预约",
            "预期结果": "系统提示需要先关注公众号，无法进行预约",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_012",
            "用例标题": "个人信息绑定-首次绑定",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已关注公众号但未绑定个人信息",
            "测试步骤": "1. 进入个人信息绑定页面\n2. 填写姓名、手机号、身份证号、单位名称\n3. 提交绑定信息",
            "预期结果": "绑定成功，信息存入数据库",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_H5_013",
            "用例标题": "个人信息绑定-信息校验",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已关注公众号",
            "测试步骤": "1. 进入个人信息绑定页面\n2. 填写不完整或格式错误的信息\n3. 提交绑定信息",
            "预期结果": "系统提示信息格式错误或不完整，绑定失败",
            "用例属性": "反用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_014",
            "用例标题": "预约信息自动填充验证",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已绑定个人信息并有历史预约记录",
            "测试步骤": "1. 进入场馆预约页面\n2. 在预约人员信息中输入已存在的姓名\n3. 观察其他信息是否自动填充",
            "预期结果": "根据姓名自动带出手机号、身份证号、单位名称",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_015",
            "用例标题": "个人中心查看预约记录",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户已有预约记录",
            "测试步骤": "1. 进入个人中心\n2. 查看我的预约\n3. 验证预约记录显示",
            "预期结果": "正确显示所有预约记录，包括预约状态、时间、场地等信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_H5_016",
            "用例标题": "个人中心取消预约",
            "功能模块": "H5端-预约规则",
            "前提条件": "用户有可取消的预约记录",
            "测试步骤": "1. 进入个人中心\n2. 选择要取消的预约\n3. 点击取消预约\n4. 确认取消操作",
            "预期结果": "预约成功取消，状态更新为已取消",
            "用例属性": "正用例",
            "用例类型": "流程",
            "用例等级": "P1"
        }
    ])
    
    return test_cases

def create_pc_test_cases():
    """创建PC端测试案例"""
    pc_test_cases = []
    
    # PC端测试案例
    
    # 1. 场地管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_001",
            "用例标题": "新增场地信息",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统",
            "测试步骤": "1. 进入场地管理页面\n2. 点击新增场地\n3. 填写场地基本信息\n4. 设置场地容量限制\n5. 保存场地信息",
            "预期结果": "场地信息新增成功，在场地列表中显示",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_002",
            "用例标题": "修改场地信息",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 选择要修改的场地\n3. 点击编辑\n4. 修改场地信息\n5. 保存修改",
            "预期结果": "场地信息修改成功，更新后的信息正确显示",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_003",
            "用例标题": "编辑场地开放时间",
            "功能模块": "PC端-场地管理",
            "前提条件": "管理员已登录PC端系统，存在场地信息",
            "测试步骤": "1. 进入场地管理页面\n2. 选择场地\n3. 点击编辑开放时间\n4. 设置不同日期的开放时间段\n5. 保存设置",
            "预期结果": "开放时间设置成功，H5端预约时间段相应更新",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        }
    ])
    
    # 2. 订单管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_004",
            "用例标题": "查看所有预约订单",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，系统中存在预约订单",
            "测试步骤": "1. 进入订单管理页面\n2. 查看订单列表\n3. 验证订单信息显示完整性",
            "预期结果": "正确显示所有预约订单，包括订单号、预约时间、场地、状态等信息",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P0"
        },
        {
            "用例编号": "TC_PC_005",
            "用例标题": "查看订单报名人员详情",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在预约订单",
            "测试步骤": "1. 进入订单管理页面\n2. 选择某个订单\n3. 点击查看报名人员\n4. 验证人员信息显示",
            "预期结果": "正确显示该订单的所有报名人员信息，包括姓名、手机号、身份证号、单位名称",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_006",
            "用例标题": "订单状态筛选",
            "功能模块": "PC端-订单管理",
            "前提条件": "管理员已登录PC端系统，存在不同状态的订单",
            "测试步骤": "1. 进入订单管理页面\n2. 使用订单状态筛选功能\n3. 分别筛选已预约、已取消、已完成等状态\n4. 验证筛选结果",
            "预期结果": "筛选功能正常，只显示对应状态的订单",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        }
    ])
    
    # 3. 用户管理模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_007",
            "用例标题": "查看所有绑定用户信息",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在绑定用户",
            "测试步骤": "1. 进入用户管理页面\n2. 查看用户列表\n3. 验证用户信息显示完整性",
            "预期结果": "正确显示所有绑定用户的个人信息和违约次数",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_008",
            "用例标题": "用户违约次数统计",
            "功能模块": "PC端-用户管理",
            "前提条件": "管理员已登录PC端系统，存在有违约记录的用户",
            "测试步骤": "1. 进入用户管理页面\n2. 查看用户违约次数\n3. 验证违约次数统计准确性",
            "预期结果": "违约次数统计准确，与实际违约情况一致",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        }
    ])
    
    # 4. 订单统计模块
    pc_test_cases.extend([
        {
            "用例编号": "TC_PC_009",
            "用例标题": "按时间段统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在历史订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 设置时间段筛选条件\n3. 点击统计\n4. 查看统计结果",
            "预期结果": "正确统计指定时间段内的订单数量和相关数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_010",
            "用例标题": "按订单状态统计",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在不同状态的订单",
            "测试步骤": "1. 进入订单统计页面\n2. 设置订单状态筛选条件\n3. 点击统计\n4. 查看统计结果",
            "预期结果": "正确统计不同状态订单的数量和占比",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P1"
        },
        {
            "用例编号": "TC_PC_011",
            "用例标题": "综合条件统计订单",
            "功能模块": "PC端-订单统计",
            "前提条件": "管理员已登录PC端系统，存在历史订单数据",
            "测试步骤": "1. 进入订单统计页面\n2. 同时设置时间段和订单状态筛选条件\n3. 点击统计\n4. 查看统计结果",
            "预期结果": "正确统计满足多个条件的订单数据",
            "用例属性": "正用例",
            "用例类型": "功能",
            "用例等级": "P2"
        }
    ])
    
    return pc_test_cases

def generate_excel_file():
    """生成Excel测试案例文档"""
    # 获取H5端和PC端测试案例
    h5_cases = create_test_cases()
    pc_cases = create_pc_test_cases()
    
    # 合并所有测试案例
    all_cases = h5_cases + pc_cases
    
    # 创建DataFrame
    df = pd.DataFrame(all_cases)
    
    # 重新排列列的顺序
    column_order = [
        "用例编号", "用例标题", "功能模块", "前提条件", 
        "测试步骤", "预期结果", "用例属性", "用例类型", "用例等级"
    ]
    df = df[column_order]
    
    # 生成Excel文件
    filename = f"嵊州总工会场馆预约系统功能测试案例_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入所有测试案例到主表
        df.to_excel(writer, sheet_name='功能测试案例', index=False)
        
        # 按模块分别创建工作表
        h5_df = pd.DataFrame(h5_cases)[column_order]
        pc_df = pd.DataFrame(pc_cases)[column_order]
        
        h5_df.to_excel(writer, sheet_name='H5端测试案例', index=False)
        pc_df.to_excel(writer, sheet_name='PC端测试案例', index=False)
        
        # 调整列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    return filename

if __name__ == "__main__":
    try:
        filename = generate_excel_file()
        print(f"测试案例Excel文档已生成: {filename}")
        print(f"共生成 {len(create_test_cases()) + len(create_pc_test_cases())} 个测试案例")
        print("\n测试案例包含以下模块:")
        print("H5端:")
        print("- 首页模块")
        print("- 场馆预约模块") 
        print("- 预约规则模块")
        print("\nPC端:")
        print("- 场地管理模块")
        print("- 订单管理模块")
        print("- 用户管理模块")
        print("- 订单统计模块")
    except Exception as e:
        print(f"生成Excel文档时出错: {str(e)}")
